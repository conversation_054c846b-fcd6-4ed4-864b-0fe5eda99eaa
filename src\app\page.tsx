import PageWrapper from '@/components/layout/PageWrapper'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid
} from '@/components/shapes/AnimatedShapes'
import { RoundedRectangle } from '@/components/shapes/Rectangle'
import LoadTimeDisplay from '@/components/ui/LoadTimeDisplay'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="animated-section relative px-6 py-6 md:px-12 lg:px-24 lg:py-0 overflow-hidden flex items-center lg:h-[calc(100vh-6.5rem)]">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content - Prioritized for LCP */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-none">
                What matters, made real.
              </h1>
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a WordPress agency that builds custom WordPress websites and web applications.
                We specialize in clean, fast, and scalable web development - nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>

            {/* Organic Geometric Composition - Deferred */}
            <div className="relative h-96 lg:h-[400px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* Things We Love Building Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden bg-bauhaus-black">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="text-display font-bold text-center mb-8 text-brand-background">What We Build</h2>
          <p className="text-xl text-center text-gray-300 mb-16 max-w-3xl mx-auto">
            Custom WordPress websites and modern web applications. Built to last, optimized to perform.
          </p>

          {/* Asymmetric Mosaic Layout */}
          <div className="grid grid-cols-12 gap-6">
            {/* Large Feature Card - WordPress Websites */}
            <div className="col-span-12 md:col-span-7 lg:col-span-8 relative group">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-8 md:p-12 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="subtle" animationIndex={35} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-bauhaus-blue text-white px-4 py-2 rounded-full text-sm font-bold mb-6">
                    WORDPRESS
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4 text-bauhaus-black">Custom WordPress websites</h3>
                  <p className="text-base text-gray-700 leading-relaxed max-w-lg">
                    Built from scratch with Sage, Blade templating, and custom Gutenberg blocks. Clean, fast, and maintainable. No bloat, no shortcuts.
                  </p>
                </div>

                {/* WordPress blocks visualization */}
                <div className="absolute bottom-8 right-8 opacity-80">
                  <div className="relative">
                    <div className="absolute right-6 bottom-12 opacity-80">
                        <AnimatedSoftCircle size="xl" color="blue" className="w-16 h-16" animationPreset="gentle" animationIndex={36} />
                    </div>
                    <div className="absolute -right-6 -top-2">
                      <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-8 h-3" animationPreset="pulse" animationIndex={37} />
                    </div>
                    <div className="absolute -left-6 bottom-4 opacity-90">
                      <AnimatedRoundedRectangle width="lg" height="sm" color="yellow" className="w-10 h-3" animationPreset="pulse" animationIndex={38} />
                    </div>
                    <div className="absolute -right-6 top-10">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="black" className="w-6 h-3" animationPreset="pulse" animationIndex={39} />
                    </div>
                  </div>
                </div>

                {/* Floating accent shapes */}
                <div className="absolute top-12 right-20 opacity-60">
                  <AnimatedTriangle size="sm" color="red" direction="up" className="w-6 h-6" animationPreset="dynamic" animationIndex={40} />
                </div>
                <div className="absolute top-1/3 right-1/4 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={41} />
                </div>
              </div>
            </div>

            {/* Tall Card - Modern Web Apps */}
            <div className="col-span-12 md:col-span-5 lg:col-span-4 relative">
              <div className="h-full bg-bauhaus-red text-white border-3 border-bauhaus-red rounded-3xl p-6 md:p-8 flex flex-col justify-between relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-30">
                  <AnimatedSoftGrid className="w-full h-full text-white" opacity="default" animationPreset="drift" animationIndex={42} />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="inline-block bg-brand-background text-bauhaus-red px-4 py-2 rounded-full text-sm font-bold mb-6">
                    MODERN
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4">React & Next.js web apps</h3>
                  <p className="text-base text-red-100 leading-relaxed">
                    Interactive, fast, and built for scale. When WordPress isn't enough, we build custom solutions that perform.
                  </p>
                </div>

                {/* Component stack visualization */}
                <div className="relative mt-8">
                  <div className="flex space-x-2 mb-2">
                    <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-6" animationPreset="gentle" animationIndex={43} />
                    <div className="w-8 h-6 bg-brand-background rounded-lg"></div>
                  </div>
                  <div className="flex space-x-2 mb-2">
                    <div className="w-12 h-6 bg-brand-background rounded-lg"></div>
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={46} />
                  </div>
                  <div className="flex space-x-2">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={47} />
                    <div className="w-8 h-6 bg-brand-background rounded-lg"></div>
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={49} />
                  </div>
                </div>
              </div>
            </div>

            {/* Wide Card - Performance & Speed */}
            <div className="col-span-12 md:col-span-8 relative">
              <div className="h-full bg-bauhaus-yellow text-bauhaus-black border-3 border-bauhaus-yellow rounded-3xl p-6 md:p-8 flex items-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="pulse" animationIndex={50} />
                </div>

                {/* Content */}
                <div className="relative z-10 w-full">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="inline-block bg-bauhaus-black text-bauhaus-yellow px-4 py-2 rounded-full text-sm font-bold mb-6">
                        PERFORMANCE
                      </div>
                      <h3 className="text-xl md:text-2xl font-bold mb-4">Lightning-fast websites</h3>
                      <p className="text-base text-gray-800 leading-relaxed max-w-md mb-6">
                        Optimized for speed from day one. Clean code, efficient assets, and smart caching, because every millisecond matters.
                      </p>
                    </div>

                    {/* Speed visualization - restored animated graphics */}
                    <div className="relative ml-8 hidden md:block">
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="energetic" animationIndex={51} />
                          <AnimatedRoundedRectangle width="xl" height="sm" color="black" className="w-24 h-4" animationPreset="flowing" animationIndex={52} />
                        </div>
                        <div className="flex items-center space-x-2">
                          <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="energetic" animationIndex={53} />
                          <AnimatedRoundedRectangle width="lg" height="sm" color="black" className="w-20 h-4" animationPreset="flowing" animationIndex={54} />
                        </div>
                        <div className="flex items-center space-x-2">
                          <AnimatedSoftCircle size="sm" color="black" className="w-4 h-4" animationPreset="energetic" animationIndex={55} />
                          <AnimatedRoundedRectangle width="md" height="sm" color="black" className="w-16 h-4" animationPreset="flowing" animationIndex={56} />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Live Performance Showcase - Full Width */}
                  <div className="bg-bauhaus-black text-bauhaus-yellow px-4 py-4 rounded-xl w-full mt-6">
                    <LoadTimeDisplay className="text-bauhaus-yellow" />
                  </div>
                </div>


              </div>
            </div>

            {/* Square Card - Maintenance & Support */}
            <div className="col-span-12 md:col-span-4 relative">
              <div className="h-full bg-brand-background border-3 border-brand-background rounded-3xl p-6 md:p-8 flex flex-col justify-center relative overflow-hidden">
                {/* Background grid */}
                <div className="absolute inset-0 opacity-40">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="default" animationPreset="gentle" animationIndex={57} />
                </div>

                {/* Content */}
                <div className="relative z-10 text-center">
                  <div className="inline-block bg-bauhaus-blue text-white px-4 py-2 rounded-full text-sm font-bold mb-6">
                    RELIABLE
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4 text-bauhaus-black">Ongoing maintenance & support</h3>
                  <p className="text-base text-gray-700 leading-relaxed">
                    We build it, we maintain it. Longterm partnerships that keep your site running smoothly.
                  </p>
                </div>

                {/* Support visualization */}
                <div className="absolute inset-0 flex items-center justify-center opacity-60">
                  <div className="relative">
                    <div className="absolute bottom-12 left-6">
                        <AnimatedSoftCircle size="lg" color="blue" className="w-12 h-12" animationPreset="gentle" animationIndex={58} />
                    </div>
                    <div className="absolute -top-6 -right-4">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-4 h-4" animationPreset="gentle" animationIndex={59} />
                    </div>
                    <div className="absolute bottom-16 -right-6">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={60} />
                    </div>
                    <div className="absolute bottom-24 left-24">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-4 h-4" animationPreset="gentle" animationIndex={61} />
                    </div>
                    <div className="absolute bottom-6 -right-24">
                      <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="gentle" animationIndex={62} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Floating background elements */}
        <div className="absolute top-8 left-8 opacity-30">
          <AnimatedSoftCircle size="xl" color="blue" className="w-20 h-20" animationPreset="drift" animationIndex={63} />
        </div>
        <div className="absolute bottom-8 right-8 opacity-30">
          <AnimatedTriangle size="xl" color="red" direction="up" className="w-16 h-16" animationPreset="float" animationIndex={64} />
        </div>
        <div className="absolute top-1/2 right-4 opacity-20">
          <AnimatedRoundedRectangle width="lg" height="xl" color="yellow" className="w-8 h-24" animationPreset="gentle" animationIndex={65} />
        </div>
        <div className="absolute top-1/4 left-1/4 opacity-20">
          <AnimatedSoftCircle size="lg" color="yellow" className="w-16 h-16" animationPreset="float" animationIndex={66} />
        </div>
        <div className="absolute bottom-1/4 left-8 opacity-25">
          <AnimatedRoundedRectangle width="md" height="lg" color="blue" className="w-6 h-16" animationPreset="drift" animationIndex={67} />
        </div>
      </section>

      {/* How We Work Section */}
      <section className="animated-section px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">How We Work</h2>

          <div className="space-y-24">
            {/* Process 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={0} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="gentle" animationIndex={1} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-4 right-8 opacity-80">
                  <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-12 h-8" animationPreset="flowing" animationIndex={2} />
                </div>
                <div className="absolute bottom-8 left-16 opacity-80">
                  <AnimatedSoftCircle size="md" color="red" className="w-10 h-10" animationPreset="gentle" animationIndex={3} />
                </div>
                <div className="absolute top-1/2 right-4 opacity-70">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="energetic" animationIndex={4} />
                </div>
                <div className="absolute top-8 left-8 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="red" className="w-6 h-3" animationPreset="drift" animationIndex={5} />
                </div>
                <div className="absolute bottom-4 right-16 opacity-70">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-6 h-6" animationPreset="float" animationIndex={6} />
                </div>
                <div className="absolute top-16 right-20 opacity-50">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-4 h-4" animationPreset="pulse" animationIndex={7} />
                </div>
                <div className="absolute bottom-16 left-4 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-4 h-4" animationPreset="flowing" animationIndex={8} />
                </div>
                <div className="absolute top-1/3 left-1/3 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="drift" animationIndex={9} />
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Listen</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We start by understanding exactly what you're trying to do, and more importantly, why.
                </p>
              </div>
            </div>

            {/* Process 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6 text-center lg:text-right lg:order-1">
                <h3 className="text-heading font-bold">Design</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We strip away noise, leaving only what needs to be there. Everything has a reason.
                </p>
              </div>
              <div className="relative h-64 w-full lg:order-2">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={10} />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-end items-center h-full">
                  <AnimatedRoundedRectangle width="xl" height="xl" color="yellow" className="w-32 h-24" animationPreset="gentle" animationIndex={11} />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-6 left-8 opacity-80">
                  <AnimatedSoftCircle size="lg" color="blue" className="w-14 h-14" animationPreset="energetic" animationIndex={12} />
                </div>
                <div className="absolute bottom-4 right-20 opacity-80">
                  <AnimatedTriangle size="md" color="red" direction="up" className="w-8 h-8" animationPreset="dynamic" animationIndex={13} />
                </div>
                <div className="absolute top-1/3 left-4 opacity-70">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-6 h-6" animationPreset="pulse" animationIndex={14} />
                </div>
                <div className="absolute bottom-1/3 left-1/3 opacity-70">
                  <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="flowing" animationIndex={15} />
                </div>
                <div className="absolute top-4 right-4 opacity-60">
                  <AnimatedTriangle size="sm" color="blue" direction="up" className="w-5 h-5" animationPreset="drift" animationIndex={16} />
                </div>
                <div className="absolute bottom-8 left-20 opacity-60">
                  <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-8" animationPreset="gentle" animationIndex={17} />
                </div>
                <div className="absolute top-20 left-16 opacity-50">
                  <AnimatedSoftCircle size="sm" color="yellow" className="w-4 h-4" animationPreset="float" animationIndex={18} />
                </div>
                <div className="absolute bottom-20 right-8 opacity-60">
                  <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-3 h-3" animationPreset="pulse" animationIndex={19} />
                </div>
                <div className="absolute top-1/2 left-1/2 opacity-50">
                  <AnimatedRoundedRectangle width="sm" height="sm" color="blue" className="w-3 h-3" animationPreset="drift" animationIndex={20} />
                </div>
              </div>
            </div>

            {/* Process 3 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <AnimatedSoftGrid className="w-full h-full text-black" opacity="hero" animationPreset="subtle" animationIndex={21} />
                </div>
                {/* Building blocks composition */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  {/* Base foundation - larger and more prominent */}
                  <div className="absolute bottom-12">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="red" className="w-40 h-12" animationPreset="flowing" animationIndex={22} />
                  </div>
                  {/* Middle layer */}
                  <div className="absolute bottom-20 left-4">
                    <AnimatedRoundedRectangle width="xl" height="lg" color="blue" className="w-32 h-12" animationPreset="energetic" animationIndex={23} />
                  </div>
                  {/* Top layer */}
                  <div className="absolute bottom-28 left-8">
                    <AnimatedRoundedRectangle width="lg" height="lg" color="yellow" className="w-24 h-12" animationPreset="pulse" animationIndex={24} />
                  </div>
                  {/* Connecting elements - larger and more visible */}
                  <div className="absolute bottom-16 right-4 opacity-80">
                    <AnimatedSoftCircle size="md" color="yellow" className="w-10 h-10" animationPreset="dynamic" animationIndex={25} />
                  </div>
                  <div className="absolute bottom-24 right-8 opacity-80">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="drift" animationIndex={26} />
                  </div>
                  <div className="absolute bottom-32 right-12 opacity-70">
                    <AnimatedTriangle size="sm" color="blue" direction="up" className="w-6 h-6" animationPreset="energetic" animationIndex={27} />
                  </div>
                  {/* Dense decorative elements */}
                  <div className="absolute top-8 right-4 opacity-70">
                    <AnimatedTriangle size="md" color="blue" direction="up" className="w-8 h-8" animationPreset="flowing" animationIndex={28} />
                  </div>
                  <div className="absolute top-16 left-4 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" animationPreset="gentle" animationIndex={29} />
                  </div>
                  <div className="absolute top-4 left-12 opacity-60">
                    <AnimatedSoftCircle size="sm" color="red" className="w-6 h-6" animationPreset="float" animationIndex={30} />
                  </div>
                  <div className="absolute top-12 right-16 opacity-50">
                    <AnimatedRoundedRectangle width="sm" height="md" color="red" className="w-3 h-6" animationPreset="pulse" animationIndex={31} />
                  </div>
                  <div className="absolute top-20 left-16 opacity-50">
                    <AnimatedTriangle size="sm" color="yellow" direction="up" className="w-4 h-4" animationPreset="drift" animationIndex={32} />
                  </div>
                  <div className="absolute top-6 left-20 opacity-40">
                    <AnimatedSoftCircle size="sm" color="blue" className="w-4 h-4" animationPreset="float" animationIndex={33} />
                  </div>
                  <div className="absolute bottom-8 left-20 opacity-60">
                    <AnimatedRoundedRectangle width="sm" height="sm" color="yellow" className="w-4 h-4" animationPreset="flowing" animationIndex={34} />
                  </div>
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Build</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We ship clean code and scalable systems. Fast. Reliable. Yours to own.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What We Build With Section - Dark Tech Directory */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="text-display font-bold text-center mb-8 text-brand-background">What We Build With</h2>
          <p className="text-xl text-center text-gray-300 mb-16 max-w-4xl mx-auto">
            We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability. No fluff, no filler.
          </p>

          {/* Clean Directory Layout */}
          <div className="max-w-6xl mx-auto">

            {/* Main Tech Stack - Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">

              {/* Core Technologies */}
              <div className="space-y-8">
                <div className="border-l-4 border-bauhaus-red pl-6">
                  <h3 className="text-2xl font-bold text-brand-background mb-2">Core Technologies</h3>
                  <p className="text-gray-400 mb-8">The foundation of everything we build</p>

                  <div className="space-y-6">
                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/wordpress.svg" alt="WordPress" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">WordPress</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Custom themes built from scratch. Clean, minimal, and made to last.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/sage.svg" alt="Sage" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Sage + Blade</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Modern WordPress workflow with elegant templating and clean structure.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/php.svg" alt="PHP" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">PHP + MySQL</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Lightweight backend logic with reliable data management.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/gutenberg.svg" alt="Gutenberg" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Gutenberg Blocks</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Native block development with PHP and JavaScript - no ACF Pro.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Frontend & Styling */}
              <div className="space-y-8">
                <div className="border-l-4 border-bauhaus-blue pl-6">
                  <h3 className="text-2xl font-bold text-brand-background mb-2">Frontend & Styling</h3>
                  <p className="text-gray-400 mb-8">Modern tools for fast, beautiful interfaces</p>

                  <div className="space-y-6">
                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/tailwind.svg" alt="Tailwind CSS" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Tailwind CSS</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Utility-first styling that keeps everything lean and consistent.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/javascript.svg" alt="JavaScript" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">JavaScript</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Vanilla JS for interactions and custom block logic - nothing unnecessary.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/webpack.svg" alt="Webpack" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Webpack</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Reliable bundling, asset optimization, and proven build workflows.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/scss.svg" alt="SCSS" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">SCSS</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Enhanced CSS with variables, mixins, and nested rules for maintainable styles.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Technologies - Third Column */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mt-16">

              {/* Extended Capabilities */}
              <div className="space-y-8">
                <div className="border-l-4 border-bauhaus-yellow pl-6">
                  <h3 className="text-2xl font-bold text-brand-background mb-2">Extended Capabilities</h3>
                  <p className="text-gray-400 mb-8">When projects need more power</p>

                  <div className="space-y-6">
                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/react.svg" alt="React" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">React</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">For rich UIs and interactive components - used with intent.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/next.svg" alt="Next.js" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Next.js</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Full-stack React framework for headless builds and modern web apps.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tools & Workflow */}
              <div className="space-y-8">
                <div className="border-l-4 border-brand-background pl-6">
                  <h3 className="text-2xl font-bold text-brand-background mb-2">Tools & Workflow</h3>
                  <p className="text-gray-400 mb-8">Everything we use to design and ship</p>

                  <div className="space-y-6">
                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/git.svg" alt="Git" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Git + GitHub</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Version control and collaboration for reliable project management.</p>
                        </div>
                      </div>
                    </div>

                    <div className="group">
                      <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-900 transition-colors">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-8 h-8 text-brand-background">
                            <img src="/images/icons/figma.svg" alt="Figma" className="w-full h-full" style={{filter: 'brightness(0) saturate(100%) invert(94%) sepia(6%) saturate(258%) hue-rotate(321deg) brightness(106%) contrast(92%)'}} />
                          </div>
                        </div>
                        <div>
                          <h4 className="font-bold text-lg text-brand-background mb-1">Figma</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">Design and prototyping for clean, user-focused interfaces.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Floating Background Elements */}
        <div className="absolute top-16 left-16 opacity-10">
          <AnimatedSoftGrid className="w-32 h-32 text-brand-background" opacity="hero" animationPreset="drift" animationIndex={95} />
        </div>
        <div className="absolute bottom-16 right-16 opacity-10">
          <AnimatedSoftGrid className="w-40 h-40 text-brand-background" opacity="hero" animationPreset="float" animationIndex={96} />
        </div>
        <div className="absolute top-1/3 right-8 opacity-15">
          <AnimatedRoundedRectangle width="lg" height="lg" color="red" className="w-24 h-24" animationPreset="gentle" animationIndex={97} />
        </div>
        <div className="absolute bottom-1/3 left-8 opacity-15">
          <AnimatedTriangle size="lg" color="yellow" direction="up" className="w-20 h-20" animationPreset="drift" animationIndex={98} />
        </div>
      </section>

      {/* Who We're For Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-display font-bold mb-8">Who We're For</h2>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              We work with founders, marketers, and teams who value clarity over chaos.
            </p>
          </div>

          {/* Large Grid Layout - Swiss/Bauhaus Style */}
          <div className="grid grid-cols-12 gap-6 max-w-6xl mx-auto">

            {/* Vertical Accent Block */}
            <div className="col-span-12 lg:col-span-4 bg-bauhaus-red text-white p-8 rounded-3xl flex flex-col justify-center">
              <div className="text-center">
                <div className="text-6xl md:text-7xl font-bold mb-4">01</div>
                <h4 className="text-xl font-bold mb-4">Founders</h4>
                <p className="text-red-100">Building something that matters</p>
              </div>
            </div>

            {/* Large Statement Block */}
            <div className="col-span-12 lg:col-span-8 bg-bauhaus-black text-brand-background p-8 md:p-12 rounded-3xl">
              <h3 className="text-3xl md:text-5xl font-bold leading-tight mb-6">
                People who want
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-lg">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-red rounded-full mt-2 mr-4 flex-shrink-0 flex items-center justify-center"><div className="w-1 h-1 bg-white rounded-full"></div></div>
                    <span>Smaller teams, fewer meetings</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-blue rounded-full mt-2 mr-4 flex-shrink-0 flex items-center justify-center"><div className="w-1 h-1 bg-white rounded-full"></div></div>
                    <span>Transparent communication</span>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-bauhaus-yellow rounded-full mt-2 mr-4 flex-shrink-0 flex items-center justify-center"><div className="w-1 h-1 bg-white rounded-full"></div></div>
                    <span>Clean design, clean code</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-brand-background rounded-full mt-2 mr-4 flex-shrink-0 flex items-center justify-center"><div className="w-1 h-1 bg-white rounded-full"></div></div>
                    <span>Something real, built fast</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Three Equal Blocks */}
            <div className="col-span-12 md:col-span-4 bg-bauhaus-blue text-white p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4">02</div>
              <h4 className="text-xl font-bold mb-4">Marketers</h4>
              <p className="text-blue-100">Performance that converts</p>
            </div>

            <div className="col-span-12 md:col-span-4 bg-bauhaus-yellow text-bauhaus-black p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4">03</div>
              <h4 className="text-xl font-bold mb-4">Team Leads</h4>
              <p className="text-gray-800">Clarity in every process</p>
            </div>

            <div className="col-span-12 md:col-span-4 bg-white border-3 border-bauhaus-black p-6 md:p-8 rounded-3xl text-center">
              <div className="text-5xl md:text-6xl font-bold mb-4 text-bauhaus-black">∞</div>
              <h4 className="text-xl font-bold mb-4">Scalability</h4>
              <p className="text-gray-700">Systems that grow with you</p>
            </div>
          </div>
        </div>

        {/* Bauhaus Background Elements - Large, Static, Geometric */}
        <div className="absolute top-20 right-20 w-40 h-40 border-8 border-bauhaus-red opacity-10"></div>
        <div className="absolute bottom-20 left-20 w-32 h-32 bg-bauhaus-blue opacity-5 rounded-full"></div>
        <div className="absolute top-1/2 left-10 w-4 h-40 bg-bauhaus-yellow opacity-10"></div>
        <div className="absolute top-40 right-1/4 w-20 h-20 bg-bauhaus-black opacity-5 transform rotate-45"></div>
      </section>

      {/* Final CTA Section */}
      <section className="animated-section relative px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-brand-background overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">

          {/* Asymmetric Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 items-center">

            {/* Content Area - Offset for visual interest */}
            <div className="lg:col-span-7 lg:col-start-2 space-y-8">
              <div className="space-y-6">
                <h2 className="text-display font-bold leading-tight">
                  Got something worth building?
                </h2>
                <p className="text-xl leading-relaxed text-gray-300 max-w-2xl">
                  Let's make it real. We'll help you strip it down to what matters and bring it to life.
                </p>
              </div>

              {/* CTA with supporting elements */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <Link href="/contact" className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block">
                  Get in touch
                </Link>

                {/* Supporting visual element */}
                <div className="flex items-center gap-4 text-gray-400">
                  <div className="flex space-x-1">
                    <RoundedRectangle width="sm" height="sm" color="red" className="w-3 h-3" />
                    <RoundedRectangle width="sm" height="sm" color="yellow" className="w-3 h-3" />
                    <RoundedRectangle width="sm" height="sm" color="blue" className="w-3 h-3" />
                  </div>
                  <span className="text-sm">Usually responds within 24 hours</span>
                </div>
              </div>
            </div>

            {/* Visual Element Area */}
            <div className="lg:col-span-4 relative h-64 lg:h-80">
              {/* Background grid */}
              <div className="absolute inset-0 opacity-30">
                <AnimatedSoftGrid className="w-full h-full text-brand-background" opacity="hero" animationPreset="drift" animationIndex={103} />
              </div>

              {/* Main focal shape */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <AnimatedSoftCircle size="xl" color="red" className="w-24 h-24" animationPreset="energetic" animationIndex={104} />
              </div>

              {/* Orbiting elements */}
              <div className="absolute top-8 right-8">
                <AnimatedRoundedRectangle width="lg" height="md" color="yellow" className="w-16 h-8" animationPreset="flowing" animationIndex={105} />
              </div>
              <div className="absolute bottom-12 left-8">
                <AnimatedTriangle size="md" color="blue" direction="up" className="w-10 h-10" animationPreset="dynamic" animationIndex={106} />
              </div>
              <div className="absolute top-16 left-12">
                <AnimatedRoundedRectangle width="md" height="sm" color="yellow" className="w-8 h-4" animationPreset="gentle" animationIndex={107} />
              </div>
              <div className="absolute bottom-8 right-16">
                <AnimatedSoftCircle size="sm" color="blue" className="w-6 h-6" animationPreset="float" animationIndex={108} />
              </div>

              {/* Connecting lines/paths */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none opacity-20">
                <path
                  d="M 60 80 Q 120 40 180 80 Q 240 120 300 80"
                  stroke="currentColor"
                  strokeWidth="1"
                  fill="none"
                  className="text-brand-background"
                />
                <path
                  d="M 40 160 Q 100 200 160 160 Q 220 120 280 160"
                  stroke="currentColor"
                  strokeWidth="1"
                  fill="none"
                  className="text-brand-background"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Enhanced Background Elements */}
        <div className="absolute top-12 left-12 opacity-15">
          <AnimatedSoftCircle size="xl" color="blue" className="w-32 h-32" animationPreset="drift" animationIndex={109} />
        </div>
        <div className="absolute bottom-16 right-16 opacity-15">
          <AnimatedRoundedRectangle width="xl" height="xl" color="yellow" className="w-28 h-28" animationPreset="float" animationIndex={110} />
        </div>
        <div className="absolute top-1/3 right-8 opacity-10">
          <AnimatedTriangle size="xl" color="red" direction="up" className="w-20 h-20" animationPreset="gentle" animationIndex={111} />
        </div>
        <div className="absolute bottom-1/4 left-8 opacity-12 lg:bottom-1/3">
          <AnimatedRoundedRectangle width="lg" height="xl" color="blue" className="w-12 h-32" animationPreset="energetic" animationIndex={112} />
        </div>

        {/* Subtle grid overlays */}
        <div className="absolute top-0 right-1/4 opacity-8">
          <AnimatedSoftGrid className="w-48 h-48 text-brand-background" opacity="default" animationPreset="subtle" animationIndex={113} />
        </div>
        <div className="absolute bottom-0 left-1/4 opacity-8">
          <AnimatedSoftGrid className="w-40 h-40 text-brand-background" opacity="default" animationPreset="subtle" animationIndex={114} />
        </div>
      </section>
    </PageWrapper>
  )
}
