import type { Metadata } from 'next'
import { notFound } from 'next/navigation'
import <PERSON>Wrapper from '@/components/layout/PageWrapper'
import Link from 'next/link'
import SchemaMarkup from '@/components/seo/SchemaMarkup'
import { getAllPosts, getPostBySlug, getRelatedPosts } from '@/lib/mdx'
import MDXContent from '@/components/blog/MDXContent'
import ReadingProgress from '@/components/blog/ReadingProgress'



interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found | Navhaus Blog'
    }
  }

  const baseUrl = 'https://navhaus.com'
  const postUrl = `${baseUrl}/blog/${post.slug}`
  const defaultOgImage = `${baseUrl}/images/og-image.png`
  const ogImage = post.seo.ogImage || defaultOgImage
  const twitterImage = post.seo.twitterImage || ogImage

  return {
    title: post.seo.title,
    description: post.seo.description,
    keywords: post.seo.keywords,
    alternates: {
      canonical: post.seo.canonicalUrl || postUrl,
    },
    robots: {
      index: !post.seo.noIndex,
      follow: !post.seo.noFollow,
      googleBot: {
        index: !post.seo.noIndex,
        follow: !post.seo.noFollow,
      },
    },
    openGraph: {
      title: post.seo.title,
      description: post.seo.description,
      url: postUrl,
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
      tags: post.tags,
      images: [
        {
          url: ogImage,
          alt: post.seo.ogImageAlt || post.title,
          width: 1200,
          height: 630,
        },
      ],
    },
    twitter: {
      card: post.seo.twitterCard || 'summary_large_image',
      title: post.seo.title,
      description: post.seo.description,
      images: [
        {
          url: twitterImage,
          alt: post.seo.twitterImageAlt || post.title,
        },
      ],
    },
  }
}

export async function generateStaticParams() {
  const posts = getAllPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export default function BlogPost({ params }: BlogPostPageProps) {
  const post = getPostBySlug(params.slug)

  if (!post) {
    notFound()
  }

  const baseUrl = 'https://navhaus.com'
  const postUrl = `${baseUrl}/blog/${post.slug}`
  const ogImage = post.seo.ogImage || `${baseUrl}/images/og-image.png`

  // Calculate word count for reading time
  const wordCount = post.content.split(/\s+/).length

  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": {
      "@type": "ImageObject",
      "url": ogImage,
      "width": 1200,
      "height": 630
    },
    "author": {
      "@type": "Organization",
      "name": post.author,
      "url": baseUrl
    },
    "publisher": {
      "@type": "Organization",
      "name": "Navhaus",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/images/logo.png`,
        "width": 200,
        "height": 60
      }
    },
    "datePublished": post.date,
    "dateModified": post.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": postUrl
    },
    "url": postUrl,
    "wordCount": wordCount,
    "keywords": post.seo.keywords.join(', '),
    "articleSection": post.category,
    "inLanguage": "en-US",
    ...(post.tags && post.tags.length > 0 && {
      "about": post.tags.map(tag => ({
        "@type": "Thing",
        "name": tag
      }))
    })
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema)
        }}
      />
      
      <ReadingProgress />

      <PageWrapper>
        {/* Article Header */}
        <article className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background">
          <div className="max-w-5xl mx-auto">
                {/* Breadcrumbs */}
                <nav className="mb-12">
                  <ol className="flex items-center space-x-3 text-sm text-bauhaus-black">
                    <li><Link href="/" className="hover:text-bauhaus-blue transition-colors">Home</Link></li>
                    <li>→</li>
                    <li><Link href="/blog" className="hover:text-bauhaus-blue transition-colors">Blog</Link></li>
                    <li>→</li>
                    <li className="font-medium">{post.title}</li>
                  </ol>
                </nav>

                {/* Article Header */}
                <header className="mb-16">
                  <div className="flex items-center space-x-6 text-sm text-bauhaus-black mb-8">
                    <span className="bg-bauhaus-blue text-bauhaus-white px-4 py-2 rounded-full text-xs font-bold uppercase tracking-wide">
                      {post.category}
                    </span>
                    <span className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <span>{post.readTime}</span>
                    </span>
                    <time className="flex items-center space-x-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      <span>
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </time>
                  </div>

                  <h1 className="text-5xl md:text-6xl font-bold leading-tight mb-8 text-bauhaus-black tracking-tight">
                    {post.title}
                  </h1>

                  <p className="text-2xl text-bauhaus-black leading-relaxed max-w-xl font-light">
                    {post.excerpt}
                  </p>
                </header>

                {/* Article Content */}
                <div>
                  <MDXContent content={post.content} />
                </div>

                {/* Article Footer */}
                <footer className="mt-16 pt-12 border-t border-bauhaus-black">
                  <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0">
                    <div className="text-center md:text-left">
                      <p className="text-sm text-bauhaus-black mb-2">Written by</p>
                      <p className="font-bold text-bauhaus-black text-lg">{post.author}</p>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                      <Link
                        href="/blog"
                        className="btn-primary"
                      >
                        ← Back to Blog
                      </Link>
                      <Link
                        href="/contact"
                        className="btn-red"
                      >
                        Start Your Project
                      </Link>
                    </div>
                  </div>
                </footer>
          </div>
        </article>

        {/* Related Posts */}
        <section className="hidden px-6 md:px-12 lg:px-24 py-16 bg-brand-background">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-2xl font-bold mb-8">Related Articles</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {getRelatedPosts(post.slug, post.category, 2)
                .map((relatedPost) => (
                  <article key={relatedPost.slug} className="bg-white rounded-3xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                          {relatedPost.category}
                        </span>
                        <span>{relatedPost.readTime}</span>
                      </div>
                      
                      <h3 className="text-lg font-bold text-gray-900 leading-tight">
                        <Link href={`/blog/${relatedPost.slug}`} className="hover:text-bauhaus-blue transition-colors">
                          {relatedPost.title}
                        </Link>
                      </h3>
                      
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {relatedPost.excerpt}
                      </p>
                      
                      <Link 
                        href={`/blog/${relatedPost.slug}`}
                        className="text-bauhaus-blue font-medium hover:text-bauhaus-red transition-colors text-sm"
                      >
                        Read more →
                      </Link>
                    </div>
                  </article>
                ))}
            </div>
          </div>
        </section>
      </PageWrapper>
    </>
  )
}
