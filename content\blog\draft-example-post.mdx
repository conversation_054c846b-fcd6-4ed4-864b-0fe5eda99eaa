---
title: "This is a Draft Post - Not Published"
excerpt: "This post demonstrates how to disable/unpublish blog posts using the published field in frontmatter."
date: "2024-01-15"
category: "Example"
readTime: "5 min read"
author: "Navhaus Team"
featured: false
published: false
tags: ["draft", "example", "unpublished", "blog management", "content management"]
seo:
  title: "Draft Post Example | Navhaus Blog"
  description: "Example of an unpublished blog post that won't appear on the website. Learn how to manage draft content in MDX blog posts."
  keywords: ["draft", "example", "unpublished", "blog management", "content management"]
  canonicalUrl: "https://navhaus.com/blog/draft-example-post"
  ogImage: "https://navhaus.com/images/blog/draft-example-og.jpg"
  ogImageAlt: "Draft Post Example - Blog content management"
  twitterImage: "https://navhaus.com/images/blog/draft-example-og.jpg"
  twitterImageAlt: "Draft Post Example - Managing unpublished content"
  twitterCard: "summary_large_image"
  noIndex: true
  noFollow: true
---

# This Post Won't Appear

This is an example of a blog post that has `published: false` in its frontmatter. 

Even though this file exists in the `content/blog` directory, it won't:
- Appear in the blog listing page
- Be accessible via direct URL
- Be included in related posts
- Be included in featured posts
- Generate static pages during build

## How to Use

To disable any blog post, simply add `published: false` to the frontmatter:

```yaml
---
title: "Your Post Title"
# ... other frontmatter fields
published: false
---
```

To re-enable it, either:
- Change `published: false` to `published: true`
- Remove the `published` field entirely (defaults to `true`)

This is useful for:
- Draft posts that aren't ready for publication
- Temporarily hiding posts
- Seasonal content that should only appear at certain times
